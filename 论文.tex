%% 
%% Copyright 2007-2024 Elsevier Ltd
%% 
%% This file is part of the 'Elsarticle Bundle'.
%% ---------------------------------------------
%% 
%% It may be distributed under the conditions of the LaTeX Project Public
%% License, either version 1.3 of this license or (at your option) any
%% later version.  The latest version of this license is in
%%    http://www.latex-project.org/lppl.txt
%% and version 1.3 or later is part of all distributions of LaTeX
%% version 1999/12/01 or later.
%% 
%% The list of all files belonging to the 'Elsarticle Bundle' is
%% given in the file `manifest.txt'.
%% 
%% Template article for Elsevier's document class `elsarticle'
%% with harvard style bibliographic references

\documentclass[preprint,12pt]{elsarticle}
% ========== Language & Fonts ==========
\usepackage[english]{babel}        % ✔ 设置英文环境
% ========== Math & Algorithm ==========
\usepackage{amsmath, amssymb}
\usepackage{algorithm}
\usepackage{algpseudocode}


% ========== Figures & Tables ==========
\usepackage{graphicx}     % 插入和操作图片的宏包
\usepackage{float}        % 提供了更强大的浮动体控制，例如 [H] 强制图表当前位置排版
\usepackage{caption}      % 自定义图表标题样式
\renewcommand{\figurename}{Figure} % 强制英文“Figure”作为图片标题前缀
\renewcommand{\tablename}{Table}   % 强制英文“Table”作为表格标题前缀
\usepackage{booktabs}     % 绘制专业的三线表（\toprule, \midrule, \bottomrule）
\usepackage{tabularx}     % 自动调整列宽的表格环境 tabularx

% ========== Misc ==========
\usepackage{enumitem}     % 优化和自定义列表环境（itemize, enumerate等）
\usepackage{hyperref}     % 插入超链接（如网址、目录跳转等）




%\usepackage[UTF8]{ctex}  % 支持中文
\usepackage{float}  % 导言区

%% Use the option review to obtain double line spacing
%% \documentclass[preprint,review,12pt]{elsarticle}

%% Use the options 1p,twocolumn; 3p; 3p,twocolumn; 5p; or 5p,twocolumn
%% for a journal layout:
%% \documentclass[final,1p,times]{elsarticle}
%% \documentclass[final,1p,times,twocolumn]{elsarticle}
%% \documentclass[final,3p,times]{elsarticle}
%% \documentclass[final,3p,times,twocolumn]{elsarticle}
%% \documentclass[final,5p,times]{elsarticle}
%% \documentclass[final,5p,times,twocolumn]{elsarticle}

%% For including figures, graphicx.sty has been loaded in
%% elsarticle.cls. If you prefer to use the old commands
%% please give \usepackage{epsfig}

%% The amssymb package provides various useful mathematical symbols
\usepackage{amssymb}
%% The amsmath package provides various useful equation environments.
\usepackage{amsmath}
%% The amsthm package provides extended theorem environments
%% \usepackage{amsthm}

%% The lineno packages adds line numbers. Start line numbering with
%% \begin{linenumbers}, end it with \end{linenumbers}. Or switch it on
%% for the whole article with \linenumbers.
%% \usepackage{lineno}
\usepackage{enumitem} % 用于自定义列表编号格式

\journal{Nuclear Physics B}

\journal{}
\usepackage{enumitem} %调用 enumitem 宏包

% hyperref 宏包调用，其中 colorlinks=true 表示超链接文字变色，而不是在周围画方框。
%\usepackage[colorlinks=true,linkcolor=blue,citecolor=blue,urlcolor=blue]{hyperref}
\usepackage{hyperref}

\usepackage{lineno} % 引入行号宏包
%\usepackage[linesnumbered,ruled,vlined]{algorithm2e}
\usepackage{amsmath}
%\linenumbers % 开启行号显示






\begin{document}


\begin{frontmatter}
	
\title{A Multi-Stage Landscape-Aware Differential Evolution Approach for Social Network Influence Maximization}
% \tnotetext[mytitlenote]{Fully documented templates are available in the elsarticle package on \href{http://www.ctan.org/tex-archive/macros/latex/contrib/elsarticle}{CTAN}.}

%% Group authors per affiliation:
\author[mymainaddress]{Jianxin Tang\corref{mycorrespondingauthor}}
\cortext[mycorrespondingauthor]{Corresponding author}
\ead{<EMAIL>}

\author[mymainaddress]{Juan Pang}

\address[mymainaddress]{School of Computer and Communication, Lanzhou University of Technology, Lanzhou 730050, China}
% \fntext[myfootnote]{Since 1880.}

% or include affiliations in footnotes:
% \author[mymainaddress,mysecondaryaddress]{Jianxin Tang\corref{mycorrespondingauthor}}
% \ead[url]{www.elsevier.com}

% \author[mysecondaryaddress]{Jianxin Tang\corref{mycorrespondingauthor}}
% \cortext[mycorrespondingauthor]{Corresponding author}
% \ead{<EMAIL>}

% \address[mymainaddress]{1600 John F Kennedy Boulevard, Philadelphia}
% \address[mysecondaryaddress]{360 Park Avenue South, New York}

\begin{abstract}
	The source code is available in\dots
\end{abstract}

\begin{keyword}
	Influence Maximization \sep Fitness landscape
\end{keyword}
	
\end{frontmatter}

%% Add \usepackage{lineno} before \begin{document} and uncomment 
%% following line to enable line numbers
%% \linenumbers

%% main text
%%

\linenumbers % 开启行号显示,从引言开始

%% Use \section commands to start a section
\section{Introduction}
\label{sec1}
%% Labels are used to cross-reference an item using \ref command.
Social networks are interconnected systems formed through various social relationships among individuals, playing an increasingly prominent role in domains such as information dissemination, public opinion evolution, and precision marketing.
The rapid proliferation of platforms such as Weibo, TikTok, and Twitter has led to the emergence of highly intricate information flow systems shaped by user relationships and behavioral interactions.
Against this backdrop, the Influence Maximization (IM) problem~\cite{RN1} has become one of the core challenges in social network analysis, with the objective of identifying a minimal set of seed nodes that maximize the expected spread under a specified diffusion model.
The problem is characterized by both fundamental theoretical challenges and practical significance, and has attracted sustained attention across multiple disciplines~\cite{guney2021large,wang2022maximizing,rui2023scalable}.

Since Domingos et al.~\cite{RN2} first formalized the influence maximization problem as an algorithmic task.
Kempe et al.~\cite{RN3} modeled the problem based on information diffusion models, formally defined it as a combinatorial optimization problem under the Independent Cascade (IC) and Linear Threshold (LT) models, and rigorously proved it to be NP-hard.
This work laid the theoretical foundation for the IM problem and, building upon this foundation, proposed a greedy algorithm framework grounded in the principle of submodularity. The algorithm was proven to achieve an optimal approximation ratio of $1-\frac{1}{e}$ for monotone submodular functions.
Despite strong theoretical guarantees, the greedy algorithm exhibits high computational overhead due to its reliance on large-scale Monte Carlo simulations, and its scalability to large networks is therefore significantly constrained.
Subsequent improvements, such as Cost-Efficient Lazy Forward (CELF)~\cite{RN4} and CELF++~\cite{RN5}, partially alleviated the computational burden but did not fundamentally eliminate the dependency on costly simulations. 
To overcome these limitations, a series of structural heuristic methods, including DC+~\cite{RN6}, and the fusion gravity model ~\cite{RN7}, were introduced. 
These methods rapidly evaluate node influence by integrating local and semi-global features such as node degree, k-shell, and gravity models. 
Although such methods demonstrate favorable computational efficiency, their reliance on static topological features and lack of diffusion modeling capability lead to solution quality that varies significantly with network structure, without guaranteeing optimality.
To balance search efficiency and solution quality, metaheuristic algorithms have been progressively introduced into the solution process of the IM problem. These algorithms possess global search capabilities and do not rely on gradient information, offering inherent advantages in addressing such complex optimization tasks (e.g., Particle Swarm Optimization \cite{RN9}, Differential Evolution~\cite{RN10}).
However, the solution space of the IM problem exhibits strong non-convexity, multimodality, and high nonlinearity in propagation responses~\cite{RN11}, which tends to trap the population in local structural basins and hinders effective transitions.
The blind search behavior and the lack of state awareness in metaheuristic algorithms give rise to inherent challenges in solving the IM problem. 
First, the lack of dynamic modeling between search behavior and landscape structure impedes strategy adaptation based on population evolution.
Second, the absence of a feedback mechanism hinders both the detection and escape from local optima, which in turn reduces global search capacity and convergence stability.
At its core, the lack of structural perception constitutes a fundamental bottleneck that restricts the adaptability of metaheuristic algorithms in complex search spaces.

Fitness landscape theory provides an effective tool for analyzing the structural properties of the solution space and the dynamic characteristics of search behavior in optimization problems, which facilitates a deeper understanding of population evolution mechanisms~\cite{RN12}.
Previous studies have shown that incorporating fitness landscape analysis into metaheuristic algorithms can effectively mitigate the blindness of search behaviors and improve algorithmic performance~\cite{RN13}.
As a result, the integration of fitness landscape theory into metaheuristic algorithm design has gradually evolved into an effective methodological paradigm for addressing complex optimization problems.

This study introduces fitness landscape analysis to construct a structure–behavior mapping mechanism that enhances the algorithm’s ability to recognize search states and adjust strategies adaptively.
Specifically, a landscape state-aware differential evolution algorithm (LADE) is proposed.
A multi-dimensional set of landscape state indicators is designed for the IM problem, with its core represented by a landscape state value that dynamically characterizes the coupling between search behavior and structural properties of the solution space.
Building upon this, a state-driven operator scheduling mechanism is developed to enable adaptive switching and transition of search strategies across different evolutionary stages, thereby improving the global search capability and convergence stability of the algorithm.
The main contributions of this paper are summarized as follows.
\begin{itemize}[noitemsep]
\item Construct a landscape state modeling mechanism: define a landscape state value $\lambda$ by integrating multidimensional indicators of individual distribution and propagation behavior characteristics to enable dynamic coupling modeling between search behavior and the structure of the solution space.
\item Propose a state-adaptive operator scheduling mechanism: partition the search process into four states—convergence, exploitation, exploration, and escape—driven by the landscape state value $\lambda$, adaptively match mutation operators to these states, and introduce an escape mechanism to actively jump out of local optima basins; this mechanism significantly enhances the global search capability.
\end{itemize}

The remainder of the paper is organized as follows. 
Section~\ref{sec:related} reviews related work on influence maximization and fitness landscape analysis. 
Section~\ref{sec:prelim} introduces the preliminaries of the proposed approach. 
Section~\ref{sec:implementation} details the implementation of LADE. 
Section~\ref{sec:experiment} reports the experimental results and analysis. 
Section~\ref{sec:discussion} discusses the distinctions and limitations of LADE. 
Section~\ref{sec:conclusion} concludes the paper and outlines future research directions.

\section{Related Work}
\label{sec:related}
\subsection{Approximation-Based Methods}
Research on the IM problem originated from the seminal work of Kempe et al.~\cite{RN3}, who proposed a classical greedy algorithm that iteratively selects nodes with the highest marginal gain and achieves the theoretical approximation ratio of $(1 - 1/e)$ for the first time.
However, the algorithm exhibits significant computational bottlenecks: Each evaluation of a node’s marginal gain requires tens of thousands of diffusion simulations, and repeated calculations in the iterative process lead to considerable redundant overhead.
CELF~\cite{RN4} and CELF++~\cite{RN5} introduced lazy update strategies to reduce redundancy, but simulation overhead remains substantial in large-scale networks.
In response to this challenge, a sampling framework based on reverse reachable (RR) sets was proposed~\cite{borgs2014maximizing}.
Representative methods such as IMM~\cite{tang2015influence}, TIM/TIM+~\cite{tang2014influence}, and SSA/D-SSA~\cite{nguyen2016stop} reformulate influence estimation as a set cover problem and thereby achieve notable improvements in computational efficiency.
As network size and accuracy requirements increase, the sampling and storage overhead of RR sets increases significantly and constitutes a new computational bottleneck.

To alleviate this issue, Guo et al.~\cite{guo2020influence} proposed SUBSIM, which enhances efficiency via subset sampling, and HIST, which reduces memory usage through a two-phase design.
Shahrouz et al.~\cite{shahrouz2021gim} introduced parallel methods such as gIM, leveraging GPU acceleration to significantly speed up sampling.
In addition, Rui et al.~\cite{rui2025scalable} proposed a scalable approximation framework for fair influence maximization.
By designing an unbiased estimator, the method effectively controls sampling size and achieves fair influence maximization on large-scale networks.

Overall, approximation methods provide notable advantages in accuracy and theoretical guarantees, but computational and storage costs remain key challenges for large-scale networks.
\subsection{Heuristic Methods}
Compared with the theoretical guarantees provided by approximation-based approaches, heuristic methods have been widely applied to influence maximization tasks in large-scale networks due to their simplicity and computational efficiency.
Shen et al.~\cite{zhong2022identification} introduced the Local Dimension Degree (LDD) method, which utilizes changes in the number of multi-level neighbors to improve the accuracy of key node identification.
Wang et al.~\cite{RN6} proposed a method that integrates centripetal centrality and an exclusion strategy based on multi-structural information, effectively reducing redundancy among seed nodes.
Chen et al.~\cite{RN7}developed a composite index combining node degree and average neighbor degree, along with an extension based on the gravity model, thereby enhancing the distinction of nodes with similar degrees and improving the overall propagation effect.
Guo et al.~\cite{guo2024node} presented the fusion gravity model , which incorporates node degree, K-shell, and eigenvector features to achieve a unified evaluation of both local and global node influence, resulting in greater adaptability across various network types.
Yang et al.~\cite{yang2023new}introduced a recursive clustering method based on community structure and a “peak-slope-valley” topological potential framework, enabling the decentralized placement of seed nodes across multiple communities and significantly increasing both propagation coverage and algorithmic robustness.

Although heuristic methods offer high efficiency and practical value, the complexity of network structures still constrains overall performance and the quality of solutions, highlighting the urgent need for more advanced approaches.
\subsection{Machine Learning-Based Methods}
Recent advances in machine learning, especially in deep learning and graph neural networks (GNNs), have provided novel solutions to the IM problem.
Such models utilize automated feature extraction and hierarchical structure modeling, which enables the integration of complex network properties and significantly improves the representation of node influence.
The ToupleGDD framework by Chen et al.~\cite{chen2023touplegdd} applies deep reinforcement learning and GNNs for IM, enabling effective influence modeling and strong generalization across networks.
The HCCKshell method proposed by Li et al.~\cite{li2024hcckshell} combines pre-trained language models with graph convolutional networks. Through weighted fusion of heterogeneous content and structural features together with entropy-based measures, this approach enhances node representations, diversity, and propagation coverage.
The GCNT model developed by Tang et al.~\cite{tang2024gcnt} integrates GCNs and graph Transformers, and incorporates multi-dimensional centrality and dynamic label mechanisms to strengthen the modeling of network relationships.
The BiGDN framework designed by Zhu et al.~\cite{zhu2025bigdn} centers on GNNs and deep reinforcement learning, and it adopts knowledge distillation and pre-training strategies, which help achieve both inference speed and prediction accuracy on large-scale networks.

Existing methods face limitations due to overlapping propagation paths and insufficient label design, resulting in low discriminability and quality of training labels.
On large-scale networks, frequent node representation and influence computations during the training phase lead to considerable computational complexity and resource consumption. 
\subsection{Metaheuristic-Based Methods}
Metaheuristic algorithms, recognized for their global search capabilities and adaptability, have emerged as effective tools in overcoming the limitations of traditional heuristic approaches for IM.
Gong et al.~\cite{RN9} were the first to propose discrete particle swarm optimization for influence maximization, reformulating the problem as the optimization of a local influence evaluation function  and designing discrete position and velocity update rules to improve diffusion estimation accuracy, though high computational complexity remains a bottleneck.
Subsequently, Tang et al.~\cite{tang2020discrete} enhanced local search and jumping capabilities through a discrete shuffled frog-leaping algorithm.
Khatri et al.~\cite{khatri2023influence} proposed a discrete Harris hawks optimization algorithm, where adaptive initialization and neighborhood detection strengthen the exploration of community structures.
In the evolutionary algorithm domain, Qiu et al.~\cite{RN10} developed a differential evolution algorithm based on a local-influence-descending search strategy, which improves the precision of seed node selection. However, the high complexity of influence estimation affects the algorithm’s efficiency.
To address this, Biswas et al.~\cite{biswas2022two} employed a two-stage VIKOR-assisted differential evolution, utilizing candidate pool compression and multiple mutation operators to enhance search efficiency in large-scale networks, but the quality of the candidate pool remains a challenge.
Wang et al.~\cite{wang2023multi}introduced a multi-operator evolutionary framework to increase diversity and adaptability; however, operator scheduling and parameter tuning are complex.
Chen et al.~\cite{chen2025imune} proposed IMUNE for dynamic network scenarios, specifically targeting spatio-temporal variations in UAV network structures by designing dynamic candidate set update and seed selection mechanisms, thereby improving influence coverage performance in time-varying environments.
Zhu et al.~\cite{zhu2024phee} presented PHEE, which achieves a balance between solution diversity and convergence through stage-wise evaluation and simulated annealing.
Overall, metaheuristic algorithms demonstrate significant advantages in influence maximization by improving global search ability and reducing reliance on high-cost simulations.
\subsection{Fitness Landscape Analysis}
First introduced by Wright\cite{wright1932roles}, fitness landscape theory constitutes a fundamental framework connecting optimization search space structures and algorithm performance, and has driven advances from evolutionary biology to intelligent optimization~\cite{stadler2002fitness}.
Stadler’s triplet modeling provided a foundation for landscape analysis in both continuous and combinatorial optimization~\cite{stadler2002fitness}.
Recent developments have been achieved in theoretical modeling, metric tools, and applications of fitness landscape analysis.

Applications in dynamic optimization and evolutionary processes have focused on landscape variability and robustness.
Landscape rotation strategies, by adjusting objective mappings, improve dynamic adaptability of algorithms~\cite{alza2022analysing}.
In evolutionary biology, hybridization and novel mutations reduce ruggedness and enhance accessibility of landscapes~\cite{patton2022hybridization}; 
empirical evidence confirms that, even in highly rugged multimodal landscapes, high-fitness peaks effectively guide population evolution~\cite{papkou2023rugged}.
In combinatorial optimization and scheduling, encoding schemes and neighborhood structures shape landscape properties and search performance, supporting algorithm selection and parameter tuning~\cite{tsogbetse2024influence}.
In multi-objective scheduling, landscape-based models substantially improve distributed heterogeneous flexible job-shop scheduling optimization~\cite{zhao2025multi}.
Multi-funnel structures in multi-objective solution spaces have been systematically revealed, informing structural design and performance evaluation of multi-objective optimization algorithms~\cite{ochoa2024funnels}.
The accuracy and representativeness of landscape analysis are strongly influenced by sampling strategies.
It has been empirically demonstrated that different discrete sampling methods, such as Latin hypercube sampling (LHS), significantly affect the representativeness of fitness landscape analysis outcomes~\cite{uher2023impact}.
Structural indicators, such as landscape state values, have been employed to improve the initialization and search performance of swarm intelligence algorithms ~\cite{diao2024enhanced}.
These advances offer valuable references for algorithm design in the present work.

Recent studies have employed fitness landscape analysis to investigate the distributional characteristics of key nodes in social networks, providing theoretical support for the efficient solution of the IM problem~\cite{RN13}.
Accordingly, fitness landscape techniques reveal solution distribution patterns in the search space, enhancing algorithmic perception of structural features and enabling adaptive adjustment, thereby supporting efficient optimization of the influence maximization problem.
\section{Preliminaries}
\label{sec:prelim}
\subsection{Influence Maximization Problem}
A social network is typically modeled as an undirected graph $G = (V, E)$, where $V$ denotes the set of nodes, namely users, and $E$ denotes the set of edges, namely social relationships among users.
The task of influence maximization is to select $k$ nodes as the seed set $S \subseteq V$ on the social network graph $G$ to maximize the expected number of nodes activated during the information diffusion process.
Formally, the influence maximization problem is defined in Equation~\eqref{eq:IM_problem}:
\begin{equation}
	S^* = \mathop{\arg\max}\limits_{S\subseteq V,\; |S|=k} \sigma(S)
	\label{eq:IM_problem}
\end{equation}
where $\sigma(S)$ denotes the expected influence spread of the seed set $S$, computed according to the diffusion model introduced below.
\subsection{Diffusion Model}
The Independent Cascade model serves as a fundamental information diffusion model in social network analysis.
In this model, each activated node attempts to influence every inactive neighbor $v$ independently with probability $p_{uv}$, and only once.
Each propagation attempt is independent, and diffusion iterates until no further activations occur.
\subsection{Expected Diffusion Value}
Given a seed set~$S$, the Expected Diffusion Value (EDV)~\cite{jiang2011simulated} efficiently estimates the expected number of activated nodes in the network.
EDV approximates fitness evaluation in influence maximization by analyzing the one-hop neighborhood structure and the number of connecting edges, which allows the method to avoid costly Monte Carlo simulations.
The EDV function is defined as shown in Equation~\eqref{eq:edv}:
\begin{equation}
	\mathrm{EDV}(S) = k + \sum_{i \in N^{(1)}(S) \setminus S} \left[ 1 - (1-p)^{\tau(i)} \right]
	\label{eq:edv}
\end{equation}

where $k$ is the number of seed nodes, $N^{(1)}(S)$ denotes the set of first-hop neighbors of $S$, $\tau(i)$ is the number of edges between node $i$ and the seed set $S$, and $p$ is the propagation probability.
Essentially, EDV provides an efficient and approximate estimation of influence spread, and has been proven to be both monotone and submodular, which allows for its direct application in greedy and meta-heuristic algorithms~\cite{biswas2022two}.
\subsection{Fitness Landscape}
A fitness landscape is modeled by a triplet $(X, N, f)$~\cite{stadler2002fitness}, where $X$ denotes the set of all feasible solutions, $N$ describes the neighborhood structure of solutions, and $f$ is the fitness function.
This modeling framework comprehensively characterizes the structure of the solution space and the distribution of fitness values that provides a theoretical foundation for the analysis and understanding of optimization algorithms.

The landscape state value~\cite{diao2024enhanced} further characterizes the distribution of individuals within the search space and reflects different evolutionary states of the population.
Specifically, let the population size be $NP$ and the dimension of individuals be $D$.
The average distance of the $i$-th individual is computed using Equation~\eqref{eq:avg_distance}:
\begin{equation}
	d_i = \frac{1}{NP - 1} \sum_{\substack{j=1 \\ j \neq i}}^{NP} \sqrt{ \sum_{k=1}^{D} (X_{i,k} - X_{j,k})^2 }
	\label{eq:avg_distance}
\end{equation}
where $X_{i,k}$ denotes the $k$-th component of individual $i$.
Let $d_g$ denote the distance of the best individual, and $d_{\max}$ and $d_{\min}$ denote the maximum and minimum values among all $d_i$, respectively.
The landscape state value $\lambda$ is defined in Equation~\eqref{eq:landscape_state}:
\begin{equation}
	\lambda = \frac{d_g - d_{min}}{d_{max} - d_{min}}
	\label{eq:landscape_state}
\end{equation}
$\lambda$ takes values in the range $[0, 1]$, which can be used to categorize search behavior into states such as convergence, exploitation, exploration, and escape.
\section{Proposed Algorithm}
\label{sec:implementation}
This section provides a detailed description of the LADE algorithm.
LADE incorporates landscape state modeling and an adaptive operator scheduling mechanism, enabling the search process to dynamically perceive structural characteristics of the solution space and flexibly adjust the balance between global exploration and local exploitation in response to changes in population distribution during evolution.The core framework is illustrated in Fig.~\ref{fig:framework}. 
Initially, the hybrid initialization module combines Latin hypercube sampling with degree-centrality-based heuristic sampling, effectively generating an initial population with both high quality and diversity, thereby establishing a solid foundation for subsequent search.
Subsequently, the landscape state perception module employs the Spread Difference Index (SDI) to dynamically compute the landscape state value $\lambda$, facilitating real-time perception of the structural properties of the search space.
Based on $\lambda$, the state-driven operator scheduling module partitions the search process into four distinct states and adaptively selects differential evolution operators, allowing flexible switching between global exploration and local exploitation.
The parameter adaptation module dynamically adjusts the crossover probability ($CR$) and scaling factor ($F$) according to search feedback.
On this basis, the local search module conducts neighborhood refinement around current high-quality solutions, continuously improving solution quality.

The following subsections present detailed descriptions of the design and implementation of each key module in LADE.
\begin{figure}[htpb]
	\centering
	\includegraphics[width=1.0\textwidth]{figures/framework.pdf}
	\caption{framework of the LADE.}
	\label{fig:framework}
\end{figure}

\subsection{Sampling Method}
A hybrid initialization strategy that integrates Latin Hypercube Sampling and degree-centrality-based heuristic sampling is utilized in LADE to generate an initial population with both high quality and diversity.
\subsubsection{Latin Hypercube Sampling}
LHS is a stratified sampling technique designed to generate uniformly distributed samples in high-dimensional spaces.
In this study, the network space is treated as a $d$-dimensional sampling space, where each dimension corresponds to a distinct network region.

\textbf{Network Region Partitioning.}
The network $G=(V,E)$ is partitioned into $d$ regions, $R = \{R_0, R_1, \ldots, R_{d-1}\}$, based on the shortest-path distance to the diameter path $P$ (see Equation~\eqref{eq:region_partition}):
\begin{equation}
	R_i = \left\{ v \in V : \min_{u \in P} d(v,u) = i \right\}
	\label{eq:region_partition}
\end{equation}
where $d(v,u)$ denotes the shortest-path length between nodes $v$ and $u$. Each region $R_j$ serves as one dimension in the LHS sampling space.

\textbf{LHS Sampling Procedure:}
Given sample size $N_s$, each sample is represented as $S^{(i)} = [x_1^{(i)}, x_2^{(i)}, \ldots, x_d^{(i)}]$, where $x_j^{(i)}$ indicates the sampling position in region $R_j$ for the $i$-th sample. The procedure consists of the following steps:
\begin{enumerate}[label=(\arabic*)]
	\item For each dimension $j=1,\ldots,d$, generate a random permutation $\pi_j$ of $\{1,\ldots,N_s\}$.
	\item For each sample $i=1,\ldots,N_s$, generate a uniform random variable $U_j^{(i)} \sim \mathrm{Uniform}(0,1)$.
	\item Compute the normalized sampling position:
	\begin{equation}
		z_j^{(i)} = \frac{\pi_j(i) - U_j^{(i)}}{N_s}
	\end{equation}
	\item Convert the continuous position to a node index:
	\begin{equation}
		\mathrm{idx}_j^{(i)} = \left\lfloor z_j^{(i)} \cdot |R_j| \right\rfloor
	\end{equation}
	where $|R_j|$ is the number of nodes in region $R_j$, and $\lfloor \cdot \rfloor$ denotes the floor operation.
	\item Select the corresponding node:
	\begin{equation}
		v_j^{(i)} = \mathrm{list}(R_j)[\mathrm{idx}_j^{(i)}]
	\end{equation}
	where $\mathrm{list}(R_j)$ denotes the ordered list of nodes in $R_j$.
	\item Construct the sampled solution:
	\begin{equation}
		S^{(i)} = \{ v_1^{(i)}, v_2^{(i)}, \ldots, v_d^{(i)} \}
	\end{equation}
\end{enumerate}

\textbf{Three-Stage Supplementation Mechanism.}
If the number of sampled nodes is less than $k$, a three-stage supplementation mechanism is applied.
First, one node with the highest combined centrality is selected from each region not yet covered by the current sample (one node per region), until the number of selected nodes reaches $k$ or all regions have been considered.
If the total remains insufficient, nodes with the highest combined centrality are selected from the set of bridge nodes.
If the number of nodes is still below $k$, the remaining slots are filled with nodes that are not assigned to any region and have not yet been selected (i.e., peripheral nodes), ranked in descending order of combined centrality.
The combined centrality is defined as the normalized weighted sum of degree centrality and betweenness centrality.
\subsubsection{Degree Centrality Sampling}
Given a seed set size $k$ and the required number of samples $N_s$, degree centrality sampling first selects the $k$ nodes with the highest degrees as the initial solution.
Then, for each node, with a probability of 0.5, it is replaced by a randomly selected node not present in the current solution.
This sampling process is repeated until $N_s$ distinct solutions are obtained. Finally, representative and diverse solutions are filtered from the union of degree centrality and Latin hypercube sampling results to initialize the population.

\subsection{Initialization}
In the initialization phase, a hybrid sampling strategy is adopted.
Specifically, candidate solutions are first generated separately using Latin Hypercube Sampling and a degree centrality-based heuristic.
The former ensures uniform coverage of the solution space, while the latter emphasizes the selection of locally high-quality node combinations.
Subsequently, the two sets of candidate solutions are merged.
The top-performing individuals from each sampling method are selected according to their fitness values and then combined into a high-quality solution set.
To ensure structural diversity, redundant solutions with high structural similarity are filtered out based on the Jaccard similarity coefficient.
If the resulting population size is insufficient, additional high-fitness solutions are supplemented preferentially.
If there is still a shortage, new solutions are dynamically generated by combining bridge nodes and ordinary nodes.
Ultimately, this process produces an initial population with both high fitness and structural diversity.
The pseudocode is presented in Algorithm~\ref{alg:hybrid_initialization}.
\begin{algorithm}[htbp]
	\caption{Hybrid Initialization}
	\label{alg:hybrid_initialization}
	\begin{algorithmic}[1]
		\Require lhsSolutions, scoreSolutions, pop, qualProportion, div, bridgeNodes, $G$, $p$, $k$
		\Ensure Initial population $P$
		\State allSolutions $\gets$ lhsSolutions $\cup$ scoreSolutions
		\For{each $S_i$ in allSolutions}
		\State $edvList[S_i] \gets$ \texttt{EDV}($S_i$, $G$, $p$)
		\EndFor
		\State qualNum $\gets \lfloor$ pop $\times$ qualProportion $\rfloor$
		\State lhsQuality $\gets$ \texttt{qualityFilter}(lhsSolutions, edvList, qualNum)
		\State scoreQuality $\gets$ \texttt{qualityFilter}(scoreSolutions, edvList, qualNum)
		\State combined $\gets$ lhsQuality $\cup$ scoreQuality
		\State initSet $\gets$ \texttt{diversityFilter}(combined, div)
		\State remain $\gets$ allSolutions $\setminus$ initSet
		\State sortedRemain $\gets$ remain, sorted by \texttt{EDV}($S_i$) descending
		\While{$|$initSet$|<$ pop}
		\If{sortedRemain not empty}
		\State $S \gets$ \texttt{next}(sortedRemain)
		\State initSet.\texttt{append}($S$)
		\Else
		\State $b \gets$ \texttt{randInt}(1, $\min(k, |\text{bridgeNodes}|+1)$)
		\State bridgeSample $\gets$ \texttt{randSample}(bridgeNodes, $b$)
		\State nonBridgeNodes $\gets V(G) \setminus$ bridgeNodes
		\State nonBridgeSample $\gets$ \texttt{randSample}(nonBridgeNodes, $k-b$)
		\State newS $\gets$ bridgeSample $\cup$ nonBridgeSample
		\State initSet.\texttt{append}(newS)
		\EndIf
		\EndWhile
		\State $P \gets$ \texttt{removeDuplicates}(initSet)
		\State \Return $P[:$pop$]$
	\end{algorithmic}
\end{algorithm}





\subsection{Landscape State Perception Mechanism}

To accommodate complex variations in the search space, a landscape state perception mechanism is introduced.
This mechanism enables real-time identification of the population’s distribution over the fitness landscape and supports dynamic strategy adjustment based on evolutionary states, thereby enhancing structural adaptability and avoiding stagnation near local optima.

\subsubsection{Landscape State Value Computation}

The concept of a landscape state value has been proposed to characterize the distribution of individuals in the fitness landscape and to guide adaptive parameter control~\cite{RN13}.
Drawing on this idea, a landscape state value is defined to dynamically represent population distribution, serving as the central indicator for strategy scheduling.

For any two solutions \( S_1 \) and \( S_2 \), the \textit{Spread Diversity Index} (SDI) is defined as follows in Equation~\eqref{eq:spread_diversity_index}:
\begin{equation}
	\mathrm{SDI}(S_1, S_2) = \frac{\sum_{v \in S_1 \triangle S_2} \mathrm{LFV}(v)}{\sum_{v \in S_1 \cup S_2} \mathrm{LFV}(v)}
	\label{eq:spread_diversity_index}
\end{equation}
where $S_1 \triangle S_2$ denotes the symmetric difference of the two solutions, and $\mathrm{LFV}(v)$ represents the local influence value of node $v$~\cite{RN10}.

Given a population $P = \{ S_1, S_2, \dots, S_{N_p} \}$, the average SDI for an individual $S_i$ is computed according to Equation~\eqref{eq:avg_sdi}:
\begin{equation}
	\overline{\mathrm{SDI}}_i = \frac{1}{N_p - 1} \sum_{\substack{j=1 \\ j \neq i}}^{N_p} \mathrm{SDI}(S_i, S_j)
	\label{eq:avg_sdi}
\end{equation}

Let $g$ denote the current best individual, and let $\overline{\mathrm{SDI}}_g$ represent its average SDI. Denote the minimum and maximum average SDI values in the population as $\mathrm{SDI}_{\min}$ and $\mathrm{SDI}_{\max}$, respectively.
The landscape state value $\lambda$ is calculated according to Equation~\eqref{eq:lambda_calculation}:
\begin{equation}
	\lambda = \frac{\overline{\mathrm{SDI}}_g - \mathrm{SDI}_{\min}}{\mathrm{SDI}_{\max} - \mathrm{SDI}_{\min}}
	\label{eq:lambda_calculation}
\end{equation}

The computational procedure for the landscape state value is detailed in Algorithm~\ref{alg:landscape_state_computation}.

\begin{algorithm}[htbp]
	\caption{Landscape State Value Computation}
	\label{alg:landscape_state_computation}
	\begin{algorithmic}[1]
		\Require Population $P = \{S_1, S_2, \ldots, S_{N_p}\}$, distance type
		\Ensure Landscape state value $\lambda$
		\State $N_p \gets |P|$
		\If{$N_p < 2$}
		\State \Return $0.5$
		\EndIf
		\State // Compute fitness values and identify best individual
		\State $fitness\_values \gets$ \texttt{BatchFitnessEvaluation}($P$)
		\State $best\_idx \gets \arg\max(fitness\_values)$
		\State // Initialize distance matrix
		\State $distance\_matrix \gets \texttt{zeros}(N_p, N_p)$
		\For{$i = 1$ to $N_p$}
		\For{$j = i+1$ to $N_p$}
		\State $set_i \gets \texttt{set}(S_i)$, $set_j \gets \texttt{set}(S_j)$
		\If{distance type is weighted}
		\State $sym\_diff \gets set_i \triangle set_j$
		\State $union \gets set_i \cup set_j$
		\State $lfv\_diff \gets \sum_{v \in sym\_diff} \mathrm{LFV}(v)$
		\State $lfv\_union \gets \sum_{v \in union} \mathrm{LFV}(v)$
		\State $dist \gets lfv\_diff / lfv\_union$ if $lfv\_union \neq 0$ else $0$
		\Else
		\State $dist \gets |set_i \triangle set_j| / |set_i \cup set_j|$
		\EndIf
		\State $distance\_matrix[i][j] \gets dist$
		\State $distance\_matrix[j][i] \gets dist$
		\EndFor
		\EndFor
		\State // Compute average distances
		\State $avg\_distances \gets$ \texttt{mean}($distance\_matrix$, axis=1)
		\State $d_g \gets avg\_distances[best\_idx]$
		\State $d_{\max} \gets \max(avg\_distances)$
		\State $d_{\min} \gets \min(avg\_distances)$
		\If{$d_{\max} = d_{\min}$}
		\State \Return $0.5$
		\EndIf
		\State \Return $(d_g - d_{\min}) / (d_{\max} - d_{\min})$
	\end{algorithmic}
\end{algorithm}

\subsubsection{Landscape State Determination}

To enable dynamic monitoring of population distribution, the landscape state value $\lambda$ is recorded at each generation.
The most recent ten values of $\lambda$ are used to calculate the 25th and 75th percentiles, denoted as $Q_1$ and $Q_3$, respectively.
Based on the value of $\lambda$, the population is classified into one of four evolutionary states, as defined in Equation~\eqref{eq:evolutionary_states}:
\begin{equation}
	\text{State} =
	\begin{cases}
		\text{Convergence}, & \lambda \in (0, Q_1] \\
		\text{Exploitation}, & \lambda \in \left( Q_1, \frac{Q_1 + Q_3}{2} \right] \\
		\text{Exploration}, & \lambda \in \left( \frac{Q_1 + Q_3}{2}, Q_3 \right] \\
		\text{Escape}, & \lambda \in (Q_3, 1] \text{ or } (\lambda \approx 0 \text{ and fitness stagnation occurs})
	\end{cases}
	\label{eq:evolutionary_states}
\end{equation}

This mechanism enables real-time monitoring of the population distribution to dynamically identify situations where the search may become trapped in local optima.
A large value of $\lambda$ signals that the population is primarily distributed in isolated regions far from the current best solution, whereas $\lambda \approx 0$ together with prolonged stagnation in fitness indicates pronounced concentration and search stagnation.
In response, interval-triggered and condition-triggered escape activation mechanisms are designed to initiate strategies for escaping local optima under scenarios of either significant dispersion or convergence stagnation.
The detected population state subsequently informs the adaptive adjustment of mutation operators.
\subsection{State-Driven Differential Evolution}
\subsubsection{Adaptive Parameter Generation}

To adapt to dynamic changes in population states during the search process, an adaptive parameter mechanism is introduced~\cite{biswas2022two}.
In each generation, the scaling factor $F_{i}$ and the crossover probability $CR_{i}$ for each individual are generated, as shown in Equations~\eqref{eq:Fi_generation} and~\eqref{eq:CRi_generation}:
\begin{align}
	F_{i}  &\sim \mathrm{Cauchy}(\mu_{F},\, 0.1), \quad F_{i} \in [0,\, 1] \label{eq:Fi_generation}, \\
	CR_{i} &\sim \mathcal{N}(\mu_{CR},\, 0.1), \quad CR_{i} \in [0,\, 1] \label{eq:CRi_generation}.
\end{align}

During evolution, the parameter values of successful individuals in each generation are stored in a historical memory of length~$M$, and the mean values of the parameters are updated using this historical information, as shown in Equations~\eqref{eq:mu_cr_update} and~\eqref{eq:mu_f_update}:
\begin{align}
	\mu_{Cr} &= (1-c)\mu_{Cr} + c \cdot \mathrm{mean}_{A}(S_{Cr}) \label{eq:mu_cr_update}, \\
	\mu_{F}  &= (1-c)\mu_{F} + c \cdot \mathrm{mean}_{L}(S_{F}) \label{eq:mu_f_update},
\end{align}
where $S_{CR}$ and $S_{F}$ denote the sets of successful $CR$ and $F$ values in memory, and $c$ is the learning rate.
This mechanism adaptively adjusts parameter distributions to improve algorithm adaptability and search performance.
	
\subsubsection{State-Aware Mutation Strategy}
\label{sec:state_aware_mutation}
Differential Evolution utilizes individual difference vectors to drive the search process, achieving a flexible balance between global exploration and local exploitation by employing a diverse set of mutation operators~\cite{tan2022dynamic}.
Classical mutation strategies include DE/rand/1, DE/best/1, DE/rand/2, DE/best/2, and DE/current-to-best/1.
Each mutation operator prioritizes either exploration or exploitation, making them suitable for different search phases.
In accordance with landscape state awareness, four complementary mutation operators are selected.

In the convergence state, the population converges within a single attraction basin, where the fitness gradient becomes shallow and the room for local improvement is limited.
In this scenario, local perturbation operators such as \texttt{DE/best/1}, which use the global optimum as the base vector, facilitate fine-grained approximation of the basin center by leveraging the existing gradient information.
This enhances exploitation capability and accelerates convergence. The classical mutation formula is given by
\begin{equation}
	V_i^G = X_{best}^G + F \ast (X_{r2}^G - X_{r3}^G)
	\label{eq:convergence_classical}
\end{equation}

In this study, the mutation operation is designed as follows: 
taking the current best individual $X_{best}$ as the base, two distinct individuals $X_{r1}$ and $X_{r2}$ define the difference set $D = X_{r1} \ominus X_{r2}$.
The nodes in $X_{best}$ with the lowest LFV are replaced by $\lfloor F \cdot |D| \rfloor$ randomly selected nodes from $D$, where $\lfloor F \cdot |D| \rfloor$ denotes the integer part of $F \cdot |D|$, i.e., the number of nodes to be replaced.
If duplicates arise, the repair operator $R(\cdot)$ is invoked.
The resulting discrete mutation operator is formulated as
\begin{equation}
	M_i = R\left(X_{best} \oplus \left(F \cdot (X_{r1} \ominus X_{r2})\right)\right)
	\label{eq:convergence_discrete}
\end{equation}
where $F$ is the adaptive scaling factor, $\ominus$ denotes set difference ($A \ominus B = \{x \mid x \in A,\ x \notin B\}$), and $\oplus$ refers to iteratively replacing nodes in $X_{best}$ with the lowest LFV by nodes randomly chosen from the difference set.
The operator $R(\cdot)$ replaces duplicates with nodes not in the current solution, preferably those with higher degree centrality, ensuring uniqueness.

In the exploitation state, the population is distributed along the basin boundaries, which serve as gradient transition zones in the landscape and offer potential paths toward superior attraction basins.
At this stage, the gradient direction is less certain.
The \texttt{DE/current-to-best/1} mutation strategy integrates guidance from both the current individual and the global best, while introducing stochastic perturbations via differential mutation.
This approach accelerates convergence and maintains population diversity, thereby mitigating premature convergence, enhancing exploitation in favorable directions, and enabling efficient exploration of complex landscape boundaries.
The classical form is:
\begin{equation}
	V_i^G = X_i^G + F \ast \left(X_{best}^G - X_i^G\right) + F \ast \left(X_{r1}^G - X_{r2}^G\right)
\end{equation}

In this study, for each individual $X_i$, two difference sets $D_1 = X_{best} \ominus X_i$ and $D_2 = X_{r1} \ominus X_{r2}$ are constructed.
For each set, nodes with the lowest LFV in $X_i$ are replaced by randomly selected nodes from the corresponding difference set.
The numbers of replacements for $D_1$ and $D_2$ are $\lfloor F \cdot |D_1| \rfloor$ and $\lfloor F \cdot |D_2| \rfloor$, respectively.
If duplicates occur, the repair operator $R(\cdot)$ is applied.
The final mutation operator is given by:
\begin{equation}
	M_i = R\left(X_i \oplus \left(F \cdot (X_{best} \ominus X_i)\right) \oplus \left(F \cdot (X_{r1} \ominus X_{r2})\right)\right)
	\label{eq:exploitation_discrete}
\end{equation}

In the exploration phase, the population is widely distributed across multiple attraction basins, where the fitness landscape is highly complex and multimodal with weak local correlations.
Overreliance on the global optimum or individual history may restrict the search to local structures.
The \texttt{DE/rand/2} mutation strategy facilitates large jumps in the solution space by multi-source guidance and high-dimensional perturbations, thereby effectively increasing population diversity and global search capability, and enhancing the probability of escaping local optima.
The classical \texttt{DE/rand/2} mutation is defined as:
\begin{equation}
	V_i^G = X_{r1}^G + F \cdot (X_{r2}^G - X_{r3}^G) + F \cdot (X_{r4}^G - X_{r5}^G)
\end{equation}

In this study, five distinct individuals $X_{r1}, X_{r2}, X_{r3}, X_{r4}, X_{r5}$ are randomly selected.
Two difference sets are constructed: $D_1 = X_{r2} \ominus X_{r3}$ and $D_2 = X_{r4} \ominus X_{r5}$.
The nodes in $X_{r1}$ with the lowest LFV values are replaced by nodes randomly chosen from these difference sets, with the total number of replacements given by $\min \left( k, \left\lfloor F \cdot |D_1| \right\rfloor + \left\lfloor F \cdot |D_2| \right\rfloor \right)$. 
If duplicates occur, the repair operator $R(\cdot)$ is applied.
The mutation operator is formulated as:
\begin{equation}
	M_i = R\left(
	X_{r1} \oplus \left(F \cdot (X_{r2} \ominus X_{r3})\right) \oplus \left(F \cdot (X_{r4} \ominus X_{r5})\right)
	\right)
	\label{eq:exploration_discrete}
\end{equation}

In the escape state, the population stagnates within a certain landscape structure for an extended period, with no improvement in fitness, indicating an extremely small gradient or a flat region.
In this situation, heuristic search alone is insufficient to overcome the bottleneck.
A jump mechanism based on historical elite solutions or random perturbation is introduced to cross landscape barriers and reactivate global search.
Concretely, an escape candidate pool is built as follows:
For each individual, the product of the number of first-order neighbors and the spreading probability is calculated for every node (denoted as the $F$ value).
These nodes are sorted by ascending $F$.
Nodes with a degree centrality greater than the global mean, which are not present in the current solution, are selected as the candidate replacement set.
The number of replacements is determined by $\min(\lfloor F \cdot k \rfloor, k)$.
High-centrality nodes sequentially replace the nodes with lower $F$ values, producing a new solution.
If the new solution achieves higher fitness, it is added to the escape candidate pool.
Then, the escape mutation randomly selects two distinct solutions $x_1$ and $x_2$ from the escape candidate pool to form a difference set $D = x_1 \ominus x_2$, and uses nodes from $D$ to randomly replace nodes in the current individual $X_i$, with the number of replacements set to $\min(k, \lfloor F \cdot |D| \rfloor)$.
Redundancy repair $R(\cdot)$ is performed as needed.
The escape mutation operator is thus formulated as:
\begin{equation}
	M_i = R\left( X_i \oplus \left( F \cdot (x_1 \ominus x_2) \right) \right)
	\label{eq:escape_discrete}
\end{equation}

The adaptive scheduling of mutation operators, guided by landscape state awareness, optimizes the coupling between fitness structure and search behavior.
Dynamic adjustment at the micro (individual difference), meso (gradient/basin), and macro (global distribution) levels improves adaptability and global search performance across complex environments.
\begin{algorithm}[htbp]
	\caption{State-Aware Mutation Strategy}
	\label{alg:state_aware_mutation_strategy}
	\begin{algorithmic}[1]
		\Require current individual $X_i$, state, population $P$, scalingFactor $F$, size $k$, escapePool
		\Ensure Mutated individual $M_i$
		\If{state = convergence}
		\State $M_i \gets$ apply Eq.~\eqref{eq:convergence_discrete} based on $X_i$
		\ElsIf{state = exploitation}
		\State $M_i \gets$ apply Eq.~\eqref{eq:exploitation_discrete} based on $X_i$
		\ElsIf{state = exploration}
		\State $M_i \gets$ apply Eq.~\eqref{eq:exploration_discrete} based on $X_i$
		\ElsIf{state = escape}
		\State $M_i \gets$ apply Eq.~\eqref{eq:escape_discrete} based on $X_i$
		\EndIf
		\State $M_i \gets \texttt{R}(M_i)$
		\State \Return $M_i$
	\end{algorithmic}
\end{algorithm}




\subsubsection{Crossover Strategy}
The purpose of the crossover operation is to combine the advantageous characteristics of the target individual and the mutant individual, thereby generating a trial individual with potentially higher fitness. For each target individual $X_i = \{x_{i,1}, x_{i,2}, \ldots, x_{i,k}\}$ and its corresponding mutant $M_i = \{m_{i,1}, m_{i,2}, \ldots, m_{i,k}\}$, the trial individual $T_i$ is generated by the rule given in Equation~\eqref{eq:discrete_crossover}:


\begin{equation}
	\label{eq:discrete_crossover}
	T_{i,j} = 
	\begin{cases} 
		M_{i,j}, & \text{if } \mathrm{rand}(0,1) \leq CR \text{ or } j = j_{\mathrm{rand}} \\
		X_{i,j}, & \text{otherwise}
	\end{cases}
\end{equation}

where $j \in \{1, 2, \ldots, k\}$ denotes the index of the gene position, $CR \in [0,1]$ is the crossover probability, and $\mathrm{rand}(0,1)$ is a uniformly distributed random number independently generated for each dimension. Each gene is selected from the mutant individual with probability $CR$; otherwise, it is inherited from the target individual. The index $j_{\mathrm{rand}}$ is randomly chosen to ensure that at least one gene is inherited from the mutant. Since the operation may result in duplicate nodes, a repair mechanism $R(\cdot)$ is applied to handle such cases.

\subsubsection{Selection Strategy}
The selection operation determines the evolutionary direction of the population and plays a crucial role in ensuring the convergence and search efficiency of the algorithm. The differential evolution algorithm adopts a greedy selection strategy by comparing the fitness values of the trial and target individuals.The selection process for each individual is defined by Equation~\eqref{eq:discrete_selection}.


\begin{equation}
	\label{eq:discrete_selection}
	X_i = 
	\begin{cases} 
		T_i, & \text{if } \mathrm{EDV}(T_i) > \mathrm{EDV}(X_i) \\
		X_i, & \text{otherwise}
	\end{cases}
\end{equation}

where $\mathrm{EDV}(\cdot)$ denotes the evaluation function of individual fitness.

\subsection{Local Search}
To enhance the local exploitation capability of the population and promote global convergence, this paper adopts the following local search strategy in each generation:
First, a local search is performed on the current best individual in the population to improve the quality of the best solution in the current generation.
If its fitness surpasses the historical global best, the local search is further applied to the updated global best solution to explore potentially better solutions.
This mechanism ensures the convergence speed of the algorithm while enhancing its ability to jump across the solution space.
In Algorithm~\ref{alg:parallel_local_search}, the parallelization is implemented at the neighbor evaluation level. 
Specifically, for each node $u$ in $S$ and for each of its neighbors $v$, the operations of generating a candidate by replacing $u$ with $v$ and evaluating its fitness are carried out in parallel.
After all such candidates are evaluated, only the candidate with the highest fitness improvement is selected in each local search round.
This ensures that the update rule remains the same as in the serial version: at most one replacement is accepted per round, and the search path is not affected by the parallel implementation.
This avoids the potential risk of missing the best single-step improvement that may occur in a multi-point update scheme.

The detailed procedure is provided in the pseudocode (see Algorithm~\ref{alg:parallel_local_search}).
\begin{algorithm}[htbp]
	\caption{Parallel Local Search}
	\label{alg:parallel_local_search}
	\begin{algorithmic}[1]
		\Require Individual $S$
		\Ensure Optimized individual $S^*$
		\State $S^* \gets S$
		\State $improvements \gets$ empty list
		\ForAll{ $u \in S$ \textbf{in parallel} }
		\ForAll{ $v \in N(u)$ }
		\If{$v \in S^*$}
		\State \textbf{continue}
		\EndIf
		\State candidate $\gets$ replace $u$ with $v$ in $S^*$
		\State candidate $\gets \texttt{R}$(candidate)
		\If{$\texttt{EDV}(\text{candidate}) > \texttt{EDV}(S^*)$}
		\State \textbf{add} (candidate, $\texttt{EDV}$(candidate)) \textbf{to} $improvements$
		\EndIf
		\EndFor
		\EndFor
		\If{$improvements$ is not empty}
		\State $S^* \gets$ candidate in $improvements$ with maximal $\texttt{EDV}$
		\EndIf
		\State \Return $S^*$
	\end{algorithmic}
\end{algorithm}

\subsection{Time Complexity Analysis}
The time complexity of LADE mainly depends on the initial sampling size $SN$, the maximum number of generations $g$, the population size $pop$, the seed set size $k$, the number of nodes $|V|$, and the average degree $\bar{d}$.
The preprocessing stage including centrality calculation and bridge node filtering has a complexity of $O(|V| \cdot |E|)$, the initial sampling and screening stage has a complexity of $O(SN \cdot k \cdot \bar{d})$, the main evolutionary stage requires mutation, recombination, and fitness evaluation for $pop$ individuals in each generation with a total complexity of $O(g \cdot pop \cdot k \cdot \bar{d})$, and the local search stage performs at most two searches per generation for the best individual in the current generation and the updated global best individual, each with a complexity of $O(k^2\bar{d}^2)$, resulting in $O(gk^2\bar{d}^2)$ over $g$ generations.
Therefore, the total time complexity of LADE is $O(|V| \cdot |E| + SN \cdot k \cdot \bar{d} + g \cdot pop \cdot k \cdot \bar{d} + gk^2\bar{d}^2)$, where the dominant term depends on the parameter setting:
when $k\bar{d} > pop$ (which is common in practice), the dominant term is $O(gk^2\bar{d}^2)$, otherwise when $k\bar{d} < pop$, the dominant term is $O(g \cdot pop \cdot k \cdot \bar{d})$.
\section{Experimental Results}
\label{sec:experiment}
\subsection{Datasets}
To evaluate the effectiveness of the proposed algorithm, experiments were conducted on six real-world networks from various domains and three representative synthetic networks.
The real-world networks encompass a range of fields, including social media, the internet, and scientific collaboration.
Specifically, the datasets include the Blog social network (Blog), the Autonomous Systems topology (AS-733), the Feather-Lastfm-Social network from the Last.fm music platform, the Sister Cities network, the NetHEPT collaboration network in high-energy physics, and the Feather-Deezer-Social network from the Deezer music platform.

In addition, to comprehensively assess the algorithm's performance across different network structures, three classical synthetic network models were employed:
the Barabási–Albert (BA) network with scale-free properties, the Watts–Strogatz (WS) network exhibiting the small-world effect, and the Erdős–Rényi (ER) random network with uniform degree distribution.

All real-world datasets are publicly available from platforms such as SNAP\footnote{\url{https://snap.stanford.edu/data/}}, KONECT\footnote{\url{http://konect.cc/networks/}}, and the Vlado network data repository\footnote{\url{http://vlado.fmf.uni-lj.si/pub/networks/data/}}.


The basic statistics of the nine networks, including the number of nodes, number of edges, average degree, and average clustering coefficient, are summarized in Table~\ref{tab:network_stats}.



\begin{table}[htbp]
	\centering
	\caption{Statistical Properties of Experimental Networks}
	\begin{tabularx}{\textwidth}{
			c
			>{\hsize=1.6\hsize}X
			>{\hsize=0.6\hsize}X
			>{\hsize=0.6\hsize}X
			>{\hsize=0.6\hsize}X
			>{\hsize=0.6\hsize}X
		}
		\toprule
		\textbf{ID} & \textbf{Network} & \textbf{|V|} & \textbf{|E|} & $\mathbf{d_{ave}}$ & $\mathbf{c_{ave}}$ \\
		\midrule
		1 & Blog                   & 3982   & 6803   & 3.42  & 0.283   \\
		2 & As-733                 & 6474   & 13895  & 4.29  & 0.252   \\
		3 & Feather-Lastfm-Social  & 7624   & 27806  & 7.29  & 0.219   \\
		4 & Sister Cities          & 14273  & 20572  & 2.88  & 0.0426  \\
		5 & NetHEHT                & 15229  & 31376  & 4.12  & 0.498   \\
		6 & Feather-Deezer-Social  & 28281  & 92752  & 6.56  & 0.141   \\
		7 & BA                     & 3000   & 8991   & 5.99  & 0.013   \\
		8 & ER                     & 3000   & 9107   & 6.08  & 0.002   \\
		9 & WS                     & 3000   & 6000   & 3.99  & 0.36    \\
		\bottomrule
	\end{tabularx}
	\label{tab:network_stats}
\end{table}

\subsection{Baseline Algorithms}

LADE is compared against five state-of-the-art algorithms:

\begin{enumerate}[label=(\arabic*)]
	\item \textbf{CELF}~\cite{RN4}: An efficient greedy approximation algorithm that leverages the submodularity of the objective function and employs ``lazy'' marginal gain evaluation, substantially reducing the number of function computations.
	CELF maintains near-optimal solutions while significantly improving computational efficiency for influence maximization and related node selection tasks in large-scale networks. CELF is typically used as a benchmark to demonstrate improvements in solution quality and runtime.
	\item \textbf{LIDDE}~\cite{RN10}: A differential evolution algorithm that utilizes the expected diffusion impact value (EDIV) as the objective function, effectively balancing search capability and convergence speed.
	\item \textbf{DHHO}~\cite{khatri2023influence}: A discretized variant of the Harris’ Hawks Optimization (HHO) algorithm. By simulating the cooperative hunting behavior of Harris’ hawks and discretizing the original HHO search strategies, DHHO adapts to combinatorial node selection tasks, enhancing both global search ability and convergence speed.
	\item \textbf{LDD}~\cite{zhong2022identification}: A local-degree-based method for identifying influential nodes, which integrates degree centrality with neighbor structural information. By introducing local degree dimension metrics, LDD comprehensively evaluates the connection patterns between nodes and their neighbors, providing a more accurate assessment of node influence.
	\item \textbf{PHEE}~\cite{zhu2024phee}: A two-phase metaheuristic that innovatively combines random range division evolutionary search with adaptive simulated annealing. This framework ensures both diversity and global exploration, accelerates the identification of optimal seed sets, and effectively mitigates local optima while improving influence spread performance.
\end{enumerate}

For all methods, parameter settings strictly follow those in the original publications.






\section{Discussion}
\label{sec:discussion}




\section{Conclusion}
\label{sec:conclusion}

%\ref{subsec1}.

%% Use \subsection commands to start a subsection.
\subsection{Example Subsection}
\label{subsec1}

Subsection text.

%% Use \subsubsection, \paragraph, \subparagraph commands to 
%% start 3rd, 4th and 5th level sections.
%% Refer following link for more details.
%% https://en.wikibooks.org/wiki/LaTeX/Document_Structure#Sectioning_commands

\subsubsection{Mathematics}
%% Inline mathematics is tagged between $ symbols.
This is an example for the symbol $\alpha$ tagged as inline mathematics.

%% Displayed equations can be tagged using various environments. 
%% Single line equations can be tagged using the equation environment.
%\begin{equation}
%f(x) = (x+a)(x+b)
%\end{equation}
%
%%% Unnumbered equations are tagged using starred versions of the environment.
%%% amsmath package needs to be loaded for the starred version of equation environment.
%\begin{equation*}
%1 - \frac{1}{e}
%\end{equation*}
%
%%% align or eqnarray environments can be used for multi line equations.
%%% & is used to mark alignment points in equations.
%%% \\ is used to end a row in a multiline equation.
%\begin{align}
% f(x) &= (x+a)(x+b) \\
%      &= x^2 + (a+b)x + ab
%\end{align}
%
%\begin{eqnarray}
% f(x) &=& (x+a)(x+b) \nonumber\\ %% If equation numbering is not needed for a row use \nonumber.
%      &=& x^2 + (a+b)x + ab
%\end{eqnarray}
%
%%% Unnumbered versions of align and eqnarray
%\begin{align*}
% f(x) &= (x+a)(x+b) \\
%      &= x^2 + (a+b)x + ab
%\end{align*}
%
%\begin{eqnarray*}
% f(x)&=& (x+a)(x+b) \\
%     &=& x^2 + (a+b)x + ab
%\end{eqnarray*}
%
%%% Refer following link for more details.
%%% https://en.wikibooks.org/wiki/LaTeX/Mathematics
%%% https://en.wikibooks.org/wiki/LaTeX/Advanced_Mathematics
%
%%% Use a table environment to create tables.
%%% Refer following link for more details.
%%% https://en.wikibooks.org/wiki/LaTeX/Tables
%\begin{table}[t]%% placement specifier
%%% Use tabular environment to tag the tabular data.
%%% https://en.wikibooks.org/wiki/LaTeX/Tables#The_tabular_environment
%\centering%% For centre alignment of tabular.
%\begin{tabular}{l c r}%% Table column specifiers
%%% Tabular cells are separated by &
%  1 & 2 & 3 \\ %% A tabular row ends with \\
%  4 & 5 & 6 \\
%  7 & 8 & 9 \\
%\end{tabular}
%%% Use \caption command for table caption and label.
%\caption{Table Caption}\label{fig1}
%\end{table}
%
%
%%% Use figure environment to create figures
%%% Refer following link for more details.
%%% https://en.wikibooks.org/wiki/LaTeX/Floats,_Figures_and_Captions
%\begin{figure}[t]%% placement specifier
%%% Use \includegraphics command to insert graphic files. Place graphics files in 
%%% working directory.
%\centering%% For centre alignment of image.
%\includegraphics{example-image-a}
%%% Use \caption command for figure caption and label.
%\caption{Figure Caption}\label{fig1}
%%% https://en.wikibooks.org/wiki/LaTeX/Importing_Graphics#Importing_external_graphics
%\end{figure}
%
%
%%% The Appendices part is started with the command \appendix;
%%% appendix sections are then done as normal sections
%\appendix
%\section{Example Appendix Section}
%\label{app1}
%
%Appendix text.
%
%%% For citations use: 
%%%       \citet{<label>} ==> Lamport [21]
%%%       \citep{<label>} ==> [21]
%%%
%Example citation, See \citet{lamport94}.
%
%%% If you have bib database file and want bibtex to generate the
%%% bibitems, please use
%%%
%%%  \bibliographystyle{elsarticle-num-names} 
%%%  \bibliography{<your bibdatabase>}
%
%%% else use the following coding to input the bibitems directly in the
%%% TeX file.
%
%%% Refer following link for more details about bibliography and citations.
%%% https://en.wikibooks.org/wiki/LaTeX/Bibliography_Management
%
%\begin{thebibliography}{00}
%
%%% For authoryear reference style
%%% \bibitem[Author(year)]{label}
%%% Text of bibliographic item
%
%\bibitem[Lamport(1994)]{lamport94}
%  Leslie Lamport,
%  \textit{\LaTeX: a document preparation system},
%  Addison Wesley, Massachusetts,
%  2nd edition,
%  1994.
%
%\end{thebibliography}

\bibliographystyle{elsarticle-num-names} 
\bibliography{ref}
\end{document}


\endinput
%%
%% End of file `elsarticle-template-num-names.tex'.
